<?php

use app\modules\foquz\assets\FoquzAsset;

$asset = FoquzAsset::register($this);
$this->title = $model->name;
$company = $model->company;
$isPaidRate = !$company->isTariffBase();


$this->registerJs("
    window.pageData = {
        poll: " . \yii\helpers\Json::encode($model) . ",
        isPaidRate: " . (int) $isPaidRate . ",
        companyTariffId: " . (int) $company->tariff->id. ",
        notificationScripts: " . \yii\helpers\Json::encode($notificationScripts) . ",
    };
", $this::POS_HEAD);

$this->registerCSSFile('/js/poll.settings.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
$this->registerJSFile('/js/poll.settings.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>

<div class="poll-settings" data-page data-bind="childrenComplete: $root.onRender.bind($root)">
    <div class="f-card f-card--shadow f-card--min-height pt-3" data-bind="descendantsComplete: function() { rendering(false) }">

        <!-- ko if: rendering && !isRequestProcessingEnabled() -->
        <div class="f-card__grow">
            <fc-spinner class="f-color-primary"></fc-spinner>
        </div>
        <!-- /ko -->
        <!-- ko if: isRequestProcessingEnabled() -->
        <div class="pl-20p pr-20p pt-20p">
            <div class="form-group mb-0">
                <switch data-bind="click: onAiToggleClick" params="checked: aiEnabled, disabled: aiControlDisabled" style="margin-top: 0; margin-bottom: 4px;">
                    <span>
                        <span data-bind="text: $parent.aiProcessingLabel"></span>
                        <fc-question params="text: $parent.aiProcessingHint"></fc-question>
                        <svg class="beta-icon" style="width:7px;height:14px;margin-left:6px;margin-bottom:8px"><use href="#beta-icon"></use></svg>
                    </span>
                </switch>
            </div>
        </div>
        <hr>
        <!-- /ko -->
        <div class="row">
            <div class="col-12 col-md-6">


                <div class="f-card__grow f-card__section pr-md-0 pb-0" style="display: none" data-bind="style: {
                display: rendering() ? 'none' : ''
            }">

                    <!-- ko if: isRequestProcessingEnabled() -->
                    <fc-label params="text: $root.pageTranslator.t('Модератор по умолчанию'), hint: $root.pageTranslator.t('Модератор по умолчанию')"></fc-label>

                    <!-- ko template: {
                            forEach: templateIf(directories.moderators.loaded(), $data),
                            afterAdd: fadeAfterAddFactory(200),
                        } -->
                    <div class="select2-wrapper" data-bind="css: {
                            'is-invalid': formControlErrorStateMatcher(moderatorId),
                            'is-valid': formControlSuccessStateMatcher(moderatorId),
                        }">
                        <select data-bind="value: moderatorId,
                                valueAllowUnset: true,
                                lazySelect2: {
                                    containerCssClass: 'form-control',
                                    wrapperCssClass: 'select2-container--form-control', minimumResultsForSearch: 10,
                                    allowClear: true,
                                    placeholder: _t('answers', 'Выберите модератора'),
                                    templateResult: userTemplateResult,
                                    disabled: blocked,
                            }">
                            <option></option>
                            <!-- ko foreach: directories.moderators.data -->
                            <option data-bind="value: id, text: name, attr: { 'data-userpic': avatar, selected: (id == $parent.moderatorId)}">
                            </option>
                            <!-- /ko -->
                        </select>
                    </div>
                    <!-- /ko -->
                    <!-- /ko -->
                </div>
            </div>
            <div class="col-12 col-md-6">
                <!-- ko if: isRequestProcessingEnabled() -->
                <div class="f-card__grow f-card__section pl-md-0 pb-0">
                    <div>
                        <fc-label params="text: $root.pageTranslator.t('Исполнитель по умолчанию'), hint: $root.pageTranslator.t('Исполнитель по умолчанию')"></fc-label>
                    </div>

                    <!-- ko template: {
                        forEach: templateIf(directories.executors.loaded(), $data),
                        afterAdd: fadeAfterAddFactory(200),
                    } -->
                    <div class="select2-wrapper" data-bind="css: {
                        'is-invalid': formControlErrorStateMatcher(executorId),
                        'is-valid': formControlSuccessStateMatcher(executorId),
                    }">
                        <select data-bind="value: executorId,
                            valueAllowUnset: true,
                            lazySelect2: {
                                containerCssClass: 'form-control',
                                wrapperCssClass: 'select2-container--form-control', minimumResultsForSearch: 10,
                                allowClear: true,
                                placeholder: _t('answers', 'Выберите исполнителя'),
                                templateResult: userTemplateResult,
                                disabled: blocked,
                        }">
                            <option></option>
                            <!-- ko foreach: directories.executors.data -->
                            <option data-bind="value: id, text: name, attr: { 'data-userpic': avatar, selected: (id == $parent.executorId)}">
                            </option>
                            <!-- /ko -->
                        </select>
                    </div>
                    <!-- /ko -->
                </div>

                <!-- /ko -->
            </div>
        </div>
        <!-- ko if: isRequestProcessingEnabled -->
        <hr>
        <!-- /ko -->
        <div class="row">
            <div class="col-12">
                <div class="px-3">
                    <!-- Время на обработку (если доступна обработка) -->
                    <!-- ko if: isRequestProcessingEnabled -->
                    <div class="poll-settings__processing-time form-group">
                        <fc-label params="text: $root.pageTranslator.t('Время на обработку анкет'), hint: $root.pageTranslator.t('Время на обработку анкет')"></fc-label>

                        <fc-time-amount params="days: processingTime.days, time: processingTime.time, errorMatcher: formControlErrorStateMatcher, successMatcher: formControlSuccessStateMatcher, disabled: blocked"></fc-time-amount>
                    </div>
                    <div class="row">
                        <!-- Время на обработку для опросов по ссылке (ручные) -->
                            <!-- ko ifnot: isAuto -->
                        <div class="col-12 col-md-6">
                            
                            <div class="poll-settings__processing-by-link form-group mb-0">
                                <fc-check params="type: 'checkbox', checked: openProcessingByLink, disabled: blocked, label: $root.pageTranslator.t('Указать другое время для пройденных по ссылке'), hint: $root.pageTranslator.t('Указать другое время для пройденных по ссылке')">
                                </fc-check>

                                <!-- ko template: {
                          foreach: templateIf(openProcessingByLink(), $data),
                          afterAdd: slideAfterAddFactory(400),
                          beforeRemove: slideBeforeRemoveFactory(400)
                        } -->
                                <div class="mt-15p">
                                    <fc-time-amount params="days: processingByLinkTime.days, time: processingByLinkTime.time, errorMatcher: formControlErrorStateMatcher, successMatcher: formControlSuccessStateMatcher, disabled: blocked"></fc-time-amount>
                                </div>
                                <!-- /ko -->
                            </div>
                            
                        </div>
                        <!-- /ko -->
                        <div class="col-12 col-md-6">
                            <!-- Время на обработку для опросов, пройденных за клиента -->
                            <div class="poll-settings__processing-for-client form-group mb-0">
                                <fc-check params="type: 'checkbox', checked: openProcessingForClient, disabled: blocked, label: $root.pageTranslator.t('Указать другое время для пройденных за респондента'), hint: $root.pageTranslator.t('Указать другое время для пройденных за респондента')"></fc-check>

                                <!-- ko template: {
                          foreach: templateIf(openProcessingForClient(), $data),
                          afterAdd: slideAfterAddFactory(400),
                          beforeRemove: slideBeforeRemoveFactory(400)
                        } -->
                                <div class="mt-15p">
                                    <fc-time-amount params="days: processingForClientTime.days, time: processingForClientTime.time, disabled: blocked, errorMatcher: formControlErrorStateMatcher, successMatcher: formControlSuccessStateMatcher"></fc-time-amount>
                                </div>
                                <!-- /ko -->
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                </div>

            </div>
        </div>
        <!-- ko if: isRequestProcessingEnabled -->
        <hr>
        <!-- /ko -->
        <div class="row">
            <div class="col-12 col-md-8">
                <!-- Сценарий уведомлений -->
                <!-- ko if: isRequestProcessingEnabled() -->
                <div class="poll-settings__notification-script form-group px-3">
                    <fc-label params="text: $root.pageTranslator.t('Сценарии уведомлений для обратной связи с респондентом')">
                        <template data-slot="hint">
                            <div style="width: 200px">
                                <?= \Yii::t('poll-settings', 'Сценарии уведомлений - это шаблоны из цепочки сообщений, по настроенным каналам связи.') ?>
                                <br>
                                <?= \Yii::t('poll-settings', 'Используются в разделе "Ответы" для быстрого разрешения конфликтов с респондентом, если в анкете был оставлен плохой отзыв.') ?>
                                <br>
                                <?= \Yii::t('poll-settings', 'Сценарии уведомления можно добавить в разделе "Настройки компании"') ?>
                            </div>
                        </template>
                    </fc-label>

                    <fc-select params="value: notificationScript, disabled: blocked, options: notificationScripts, optionsForSearch: 0, clearable: true, placeholder: $root.pageTranslator.t('Сценарий уведомления по умолчанию')"></fc-select>
                </div>
                <!-- /ko -->
            </div>
        </div>
        <!-- ko ifnot: blocked -->
        <footer class="f-card__footer" data-bind="stickyFooter">
            <div class="poll-settings__actions">

                <fc-button class="poll-settings__reset" params="label: baseTranslator.t('Отменить'), icon: 'bin', click: function() { reset() }"></fc-button>

                <fc-button class="poll-settings__submit" params="label: baseTranslator.t('Сохранить'), icon: 'save', color: 'success', click: function() { submit() }"></fc-button>
            </div>

            <fc-success params="show: showSuccessMessage"></fc-success>
        </footer>
        <!-- /ko -->
    </div>

</div>
import { formatServerDateToClient } from "@/utils/date/format";
import { ApiUrl } from "@/utils/url/api-url";
import { periodValidator } from "@/utils/validation/period";
import { fullTimeValidator, timeValidator } from "@/utils/validation/time";
import {
  PollActivateEvent,
  PollSettingsUpdateEvent,
} from "@/utils/events/poll";
import { Translator } from "@/utils/translate";
import { DialogsModule } from "@/utils/dialogs-module";

import { FmTimeAmount } from "@/entities/structures/fm-time-amount";
import { getRequestProcessingSettings } from "@/utils/project/request-processing-settings";
import { getCompanyCollections } from "@/api/collections/get-collections";
import { FmDuration } from "./models/fm-duration";

import "@/presentation/views/fc-time-amount";
import "@/presentation/views/fc-utm";
import "./components/time-point";
import "./components/confirm-modal";

import * as LangsComponent from "./components/langs";

import { registerComponent } from "@/utils/engine/register-component";

import "./style.less";
import { FmUTM } from "@/entities/structures/fm-utm";
import { DEFAULT_LANG } from "@/constants/langs";
import {copyToClipboard} from "Utils/copy-to-clipboard";
import {HidePoppersEvent} from "Utils/events/modal";
import "@/dialogs/ai-processing-request-dialog";

registerComponent("poll-langs", LangsComponent);

const BLOCK_ACTIONS =
  !CURRENT_USER || CURRENT_USER.blockActions || window.CURRENT_USER.watcher;
let queryString = window.location.search;

const tabParams = {
  main: [
    "datetime_start",
    "datetime_end",
    "defaultLang",
    "kioskMode",
    "lang",
    "point_system",
    "personal_data",
    "show_foquz_link",
    "title",
    "duration",
    "editing_duration",
    "goal",
  ],
  fillingForm: [
    "time_to_pass",
    "limit_count",
    "goalCount",
    "expiration_in_minutes",
  ],
  sendForm: [
    "dont_send_if_passed",
    "stopSendingCondition",
    "stopSendingPeriod",
    "dont_send_if_promocode_used",
    "dont_send_if_passed_link",
    "stopSendingLink",
    "stopSendingLinkCondition",
    "stopSendingLinkPeriod",
    "stopSendingLinkPeriodTime",
    "widget_display_limit_type",
    "widget_display_limit_days",
    "widget_display_limit_time",
    "stopSendingWidgetPeriod",
    "stopSendingWidgetTime",
    "pollMailingFrequency",
    "widget_display_ignore_limit",
    "widget_create_new_answer_limit",
  ],
  processingAnswers: [
    "default_moderator_id",
    "default_executor_id",
    "processing_time_in_minutes",
    "processing_time_by_link_in_minutes",
    "processing_time_for_client_in_minutes",
    "notificationScript",
    "ai_enabled",
  ],
  other: ["dictionary_id","css_self_type","css_self_url","css_self_text"],
  requaredParams: ["need_auth"],
};
const requaredParams = ["need_auth"];

class ViewModel {
  constructor(pageData) {
    DialogsModule(this);
    this.baseTranslator = Translator("main");
    this.pageTranslator = Translator("poll-settings");
    this.validationTranslator = Translator("validation");
    this.settingTab = ko.observable("main");
    this.activeTab = ko.observable("main");
    this.blocked = BLOCK_ACTIONS;
    this.deleteModalOpen = ko.observable(null);
    this.modalData = ko.observable(null);
    // AI processing observables must be defined before async init and setPollData
    this.aiEnabled = ko.observable(false);
    this.companyAiEnabled = ko.observable(false);

    // AI processing text fields
    this.aiProcessingLabel = ko.observable("Определение тональности и тегов для открытого ответа с помощью искусственного интеллекта");
    this.aiProcessingHint = ko.observable("Включите эту опцию, чтобы автоматически анализировать открытые ответы респондентов с помощью искусственного интеллекта. ИИ будет определять эмоциональную окраску текста (позитивная, нейтральная, негативная) и присваивать релевантные теги для удобной категоризации и анализа результатов опроса.");
    const { poll, companyTariffId } = window.pageData;
    let urlParams = new URLSearchParams(queryString);
    if (urlParams.get("tab") !== null) {
      let tab = urlParams.get("tab");
      if (tab) {
        this.settingTab(tab);
        this.activeTab(tab);
      }
    }

    this.directories = {
      moderators: new Directory("answers/moderators?pollId=" + poll.id),
      executors: new Directory("answers/executors?pollId=" + poll.id),
    };

    Object.keys(this.directories).forEach((key) => {
      if (["reasons", "compensations"].includes(key)) {
        this.directories[key].load("force");
        return;
      }
      this.directories[key].loaded() ? null : this.directories[key].load();
    });

    const self = this;
    this.companyCollections = ko.observableArray([]);
    this.showDictionarySelect = ko.observable(false);
    this.companyCollectionsLoaded = ko.observable(false);
    this.getUrl = (tab) => `?id=${poll.id}&tab=${tab}`;

    this.changeUrl = function (tab) {
      this.activeTab(tab);
      history.pushState({ tab: tab }, tab, `?id=${poll.id}&tab=${tab}`);
      // if (channel !== false)
      //   history.pushState(
      //     { tab: tab, channel: channel, setting: setting },
      //     tab + ": " + channel,
      //     "?tab=" + tab + "&channel=" + channel + "&setting=" + setting
      //   );
    };
    if (pageData.poll.dictionary_id) {
      this.companyCollections([
        {
          active: true,
          canRemove: false,
          count: 1, // не важно
          description: "",
          id: pageData.poll.dictionary_id,
          name: pageData.poll.dictionary_name,
          system: false,
          text: pageData.poll.dictionary_name,
        },
      ]);
      this.showDictionarySelect(true);
    } else {
      this.showDictionarySelect(true);
    }

    this.userTemplateResult = (state) => {
      if (!state.id) {
        return state.text;
      }
      const userpic =
        $(state.element).data("userpic") || "assets/img/user-placeholder-2.png";
      return $(
        `<span class="user-option">
                        <span class="user-option__icon">
                            <img class="user-option__userpic" src="${userpic}">
                        </span>
                        <span class="user-option__name">${state.text}</span>
                        </span>`
      );
    };

    // доступна обработка
    this.isRequestProcessingEnabled = ko.observable(false);
    this.aiControlDisabled = ko.computed(() => {
      return this.blocked || !this.companyAiEnabled() || !this.isRequestProcessingEnabled();
    });
    getRequestProcessingSettings().then((data) => {
      this.isRequestProcessingEnabled(data.request_processing_enabled);
      this.companyAiEnabled(!!data.company_ai_enabled);
    });

    this.isPaidRate = pageData.isPaidRate; // платный тариф
    this.companyTariffId = companyTariffId; // платный тариф

    // данные опроса
    this.pollData = ko.observable(null); // сохраненные данные
    this.isAuto = ko.computed(() => this.pollData() && this.pollData().is_auto);
    this.hasAnswers = ko.computed(
      () => this.pollData() && this.pollData().countAnswers > 0
    );

    // список сценариев
    this.notificationScripts = (pageData.notificationScripts || []).map((s) => {
      return {
        id: s.id,
        text: s.name,
      };
    });

    // UTM
    this.utm = new FmUTM();

    // валидация
    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );
    this.showSuccessMessage = ko.observable(false);

    /** ФОРМА НАСТРОЕК */

    // продолжительность (для авто)
    this.period = ko.observable({ from: "", to: "" }).extend({
      validation: {
        validator: periodValidator(),
        message: () =>
          this.validationTranslator.t("Некорректный формат интервала")(),
        onlyIf: () => this.isAuto(),
      },
    });

    // цель (кол-во ответов)
    this.targetCount = ko.observable("");

    // лимит ответов
    this.answersCount = ko.observable("");

    // заканчивать опрос (авто)
    this.endPoll = ko.observable("");

    // время прохождения
    this.timeToPass = ko.observable("").extend({
      validation: {
        validator: fullTimeValidator(),
        message: () => {
          return this.validationTranslator.t("Неверный формат")()
        },
      },
    });

    this.editingDurationEnabled = ko.observable(false);
    this.editingDuration = ko.observable("");

    this.needAuthEnabled = ko.observable(false);

    // время для заполнения после отправки
    this.expirationTime = new FmTimeAmount();

    // рестарт опроса на планшетах (ручные)
    // рестарт для вопросов
    this.timeToRestart = ko.observable("").extend({
      validation: {
        validator: (v) => {
          return v <= 300;
        },
        message: () =>
          this.pageTranslator.t(
            "Опрос должен начинаться заново не позднее, чем через 300 секунд"
          )(),
      },
    });
    // рестарт для конечного экрана
    this.timeToRestartFinish = ko.observable("").extend({
      validation: {
        validator: (v) => {
          return v <= 300;
        },
        message: () =>
          this.pageTranslator.t(
            "Опрос должен начинаться заново не позднее, чем через 300 секунд"
          )(),
      },
    });

    // время на обработку (если доступна обработка)
    this.processingTime = new FmTimeAmount();
    // обработка опросов, пройденных по ссылке
    this.openProcessingByLink = ko.observable(false);
    this.processingByLinkTime = new FmTimeAmount({
      validator: {
        onlyIf: () => this.openProcessingByLink(),
      },
    });
    this.openProcessingByLink.subscribe((v) => {
      if (!v) this.processingByLinkTime.value = null;
    });
    // обработка опросов, пройденных за клиента
    this.openProcessingForClient = ko.observable(false);
    this.processingForClientTime = new FmTimeAmount({
      validator: {
        onlyIf: () => this.openProcessingForClient(),
      },
    });
    this.openProcessingForClient.subscribe((v) => {
      if (!v) this.processingForClientTime.value = null;
    });

    // Выбрать модератора и исполнителя
    this.moderatorId = ko.observable(poll.default_moderator_id ?? "");
    this.executorId = ko.observable(poll.default_executor_id ?? "");

    this.dictionaryId = ko.observable(`${poll.dictionaryId || ""}`);


    // Выбор стилей
    this.activeTabStyle = ko.observable(`${poll.css_self_type || "0"}`);
    this.cssSelfText = ko.observable(`${poll.css_self_text || ""}`);
    this.cssSelfUrl = ko.observable(`${poll.css_self_url || ""}`).extend({
      validation: {
        validator: (v) => {
          if (this.activeTabStyle() !== 0) return true;
          if (!v || v.trim() === "") return true;

          // const urlPattern = /^https?:\/\/(?:localhost|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|[a-z0-9-]+(?:\.[a-z0-9-]+)+)(?::\d+)?(?:\/[^\s.]*[^\s.])\.css$/i;
          try {
            const url = new URL(v);

            if (!['http:', 'https:'].includes(url.protocol)) {
              return false;
            }

            if (!/\.css$/i.test(url.pathname)) {
              return false;
            }
            const hostname = url.hostname;
            const isLocalhost = hostname === "localhost";
            const isIP = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(hostname);
            const isValidDomain =
                /^([a-z0-9-]+\.)+[a-z0-9-]+$/i.test(hostname);

            if (!isLocalhost && !isIP && !isValidDomain) {
              return false;
            }

            if (url.search || url.hash) return false;

            // return urlPattern.test(v);
            return true;
          } catch {
            return false;
          }


        },
        message: () => this.pageTranslator.t("Неверный формат")(),
      },
    });

    this.activeTabStyle.subscribe(() => {
      this.cssSelfUrl.valueHasMutated();
      this.formModel.valueHasMutated();
    });

    this.clearStyles = function (){
      this.cssSelfText('');
      this.forceResizeTextarea();
    };

    this.codeCopied = ko.observable(false);
    this.copyCSS = function (){
      copyToClipboard(this.cssSelfText());
      HidePoppersEvent.emit();

      this.codeCopied(true);
    };


    this.hasScrollTopShadow = ko.observable(false);
    this.hasScrollBottomShadow = ko.observable(true);

    this.updateShadows = function(_, event) {
      var textarea = event.target;
      this.hasScrollTopShadow(textarea.scrollTop > 0);
      this.hasScrollBottomShadow(textarea.scrollTop + textarea.clientHeight < textarea.scrollHeight);
    };

    // сценарий уведомления
    this.notificationScript = ko.observable("");

    // заголовок страницы (title)
    this.title = ko.observable("");

    // описание опроса
    this.description = ko.observable("");

    // режим киоска (ручные)
    this.kioskMode = ko.observable(false);

    // подключить систему баллов
    this.pointSystem = ko.observable(false);

    // не отправлять, если опрос пройден
    this.dontSendIfPassed = ko.observable(false);

    this.dontSendIfPassedLink = ko.observable(false);

    // не отправлять, если использован промокод
    this.dontSendIfPromocodeUsed = ko.observable(false);

    // согласие на передачу данных
    this.personalData = ko.observable(false);

    // ссылка Создано в Foquz
    this.showFoquzLink = ko.observable(false);

    // ограничения показа виджета
    this.ignoreWidgetDisplay = ko.observable(false);
    this.widget_display_ignore_limit = ko.observable("").extend({
      validation: {
        validator: (v) => {
          return !this.ignoreWidgetDisplay() || v || v === 0;
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });

    // Виджет новая анкета
    this.createWidgetNewAnswerLimit = ko.observable(false);
    this.widget_create_new_answer_limit = ko.observable("").extend({
      validation: {
        validator: (v) => {
          if (!this.createWidgetNewAnswerLimit()) return true;
          
          if (v === "" || v === null || v === 0) return false;

          const numValue = parseInt(v);
          if (isNaN(numValue)) {
            return false;
          }

          return numValue >= 1 && numValue <= 999;
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });

    this.widget_create_new_answer_limit.subscribe(function (newValue) {
      if (newValue === "0") {
        this.widget_create_new_answer_limit("");
      } else if (newValue && parseInt(newValue) < 1) {
        this.widget_create_new_answer_limit("");
      }
    }, this);

    // языки
    this.useLangs = ko.observable(false);
    this.selectedLangs = ko.observableArray([]);
    this.defaultLang = ko.observable(null);

    this.useLangs.subscribe((v) => {
      if (!v) {
        this.selectedLangs([]);
        this.defaultLang(null);
      }
    });

    // не отправлять опрос повторно
    this.stopSending = ko.observable(false);
    this.stopSendingConditions = [
      {
        id: "double",
        text: "Если он уже был отправлен",
      },
      {
        id: "period",
        text: "В течение периода",
      },
    ];
    this.stopSendingCondition = ko.observable("double");
    this.stopSendingPeriod = ko.observable("").extend({
      validation: {
        validator: (v) => {
          return (
            !this.stopSending() || this.stopSendingCondition() === "double" || v
          );
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });

    // Не показывать повторно опрос в виджете клиенту
    this.stopSendingWidget = ko.observable(false);
    this.stopSendingWidgetConditions = [
      {
        id: "1",
        text: "Если он уже был отправлен",
      },
      {
        id: "2",
        text: "В течение периода",
      },
    ];

    this.stopSendingWidgetCondition = ko.observable("1");
    this.stopSendingWidgetPeriod = ko.observable("").extend({
      validation: {
        validator: (v) => {
          const tyme = this.stopSendingWidgetTime && this.stopSendingWidgetTime();
          return (
            !this.stopSendingWidget() ||
            this.stopSendingWidgetCondition() == 1 ||
            (v && v != 0) ||
            (tyme && tyme !== '00:00')
          );
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });

    this.validationMessage = ko.observable("");
    this.updateValidationMessage = function (value) {
      if (!timeValidator()(value) && value) {
        return this.validationTranslator.t("Некорректное значение")();
      } else {
        return this.validationTranslator.t("Обязательное поле")();
      }
    };

    this.stopSendingWidgetTime = ko.observable("").extend({
      validation: {
        validator: (v) => {
          if (this.stopSendingWidgetCondition() == 1 || !this.stopSendingWidget()) {
            return true
          }
          const days = this.stopSendingWidgetPeriod();
          return (
            timeValidator()(v) &&
            (!this.stopSendingWidget() ||
              this.stopSendingWidgetCondition() == 1 ||
              (days && days != 0) ||
              (v && v !== '00:00'))
          );
        },
        message: () =>
          this.stopSendingWidgetTime()
            ? this.validationTranslator.t("Некорректное значение")()
            : this.validationTranslator.t("Обязательное поле")(),
      },
    });
    this.stopSendingLink = ko.observable(false);
    this.stopSendingLinkConditions = [
      {
        id: "double",
        text: "Если она уже создана",
      },
      {
        id: "period",
        text: "В течение периода",
      },
    ];
    this.stopSendingLinkCondition = ko.observable("double");
    this.stopSendingLinkPeriod = ko.observable("").extend({
      validation: {
        validator: (v) => {
          const tyme = this.stopSendingLinkPeriodTime && this.stopSendingLinkPeriodTime();
          return (
            !this.stopSendingLink() ||
            this.stopSendingLinkCondition() === "double" ||
            (v && v != 0) ||
            (tyme && tyme !== '00:00')
          );
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });
    

    this.stopSendingLinkPeriodTime = ko.observable("").extend({
      validation: {
        validator: (v) => {
          if (this.stopSendingLinkCondition() === "double" || !this.stopSendingLink()) {
            return true
          }
          const days = this.stopSendingLinkPeriod();
          return (
            timeValidator()(v) &&
            (!this.stopSendingLink() ||
              this.stopSendingLinkCondition() === "double" ||
              (days && days != 0) ||
              (v && v !== '00:00'))
          );
        },
        message: () =>
          this.stopSendingLinkPeriodTime()
            ? this.validationTranslator.t("Некорректное значение")()
            : this.validationTranslator.t("Обязательное поле")(),
      },
    });

    this.ignoreGlobalMailingFrequency = ko.observable(false);
    this.durationEnabled = ko.observable(false);

    // продолжительность (для ручных): дата запуска, дата окончания
    this.duration = new FmDuration(this.durationEnabled);
    this.pollMailingFrequency = ko.observable("").extend({
      validation: {
        validator: (v) => {
          return !this.ignoreGlobalMailingFrequency() || v;
        },
        message: () => this.pageTranslator.t("Обязательное поле")(),
      },
    });
    this.showError = ko.computed(() => {
      return (this.isSubmitted() && !this.duration.from.date()) || (this.isSubmitted() && !this.duration.to.date());
    });

    tabParams.main.push(poll.is_auto ? "dateRange" : "");
    tabParams.fillingForm.push(poll.is_auto ? "endOffPoll" : "");
    tabParams.fillingForm.push(!poll.is_auto ? "time_to_restart" : "");
    tabParams.fillingForm.push(
      !poll.is_auto ? "time_to_restart_screen_type_end" : ""
    );
    function snakeToCamel(snakeCaseString) {
      return snakeCaseString.replace(/_([a-z])/g, (match, letter) =>
        letter.toUpperCase()
      );
    }
    this.formModel = ko.validatedObservable({});
    /** Модель для валидации и отслеживания изменений */
    const allParams = {
      period: this.period,
      targetCount: this.targetCount,
      answersCount: this.answersCount,
      endPoll: this.endPoll,
      kioskMode: this.kioskMode,
      pointSystem: this.pointSystem,
      dontSendIfPassed: this.dontSendIfPassed,
      dontSendIfPassedLink: this.dontSendIfPassedLink,
      dontSendIfPromocodeUsed: this.dontSendIfPromocodeUsed,
      personalData: this.personalData,
      notificationScript: this.notificationScript,
      description: this.description,
      timeToPass: this.timeToPass,
      stopSendingWidgetTime: this.stopSendingWidgetTime,
      timeToRestart: this.timeToRestart,
      timeToRestartFinish: this.timeToRestartFinish,
      showFoquzLink: this.showFoquzLink,
      processingTime: this.processingTime,
      openProcessingByLink: this.openProcessingByLink,
      processingByLinkTime: this.processingByLinkTime,
      openProcessingForClient: this.openProcessingForClient,
      processingForClientTime: this.processingForClientTime,
      aiEnabled: this.aiEnabled,
      expirationTime: this.expirationTime,
      widget_display_ignore_limit: this.widget_display_ignore_limit,
      widget_create_new_answer_limit: this.widget_create_new_answer_limit,
      duration: this.duration,
      utm: this.utm,
      title: this.title,
      useLangs: this.useLangs,
      selectedLangs: this.selectedLangs,
      defaultLang: this.defaultLang,
      stopSending: this.stopSending,
      stopSendingCondition: this.stopSendingCondition,
      stopSendingPeriod: this.stopSendingPeriod,
      stopSendingLink: this.stopSendingLink,
      stopSendingLinkCondition: this.stopSendingLinkCondition,
      stopSendingLinkPeriod: this.stopSendingLinkPeriod,
      stopSendingLinkPeriodTime: this.stopSendingLinkPeriodTime,
      stopSendingWidget: this.stopSendingWidget,
      stopSendingWidgetCondition: this.stopSendingWidgetCondition,
      stopSendingWidgetPeriod: this.stopSendingWidgetPeriod,
      pollMailingFrequency: this.pollMailingFrequency,
      editingDurationEnabled: this.editingDurationEnabled,
      editingDuration: this.editingDuration,
      needAuthEnabled: this.needAuthEnabled,
      moderatorId: this.moderatorId,
      executorId: this.executorId,
      dictionaryId: this.dictionaryId,
      ignoreGlobalMailingFrequency: this.ignoreGlobalMailingFrequency,
      durationEnabled: this.durationEnabled,
      ignoreWidgetDisplay: this.ignoreWidgetDisplay,
      createWidgetNewAnswerLimit: this.createWidgetNewAnswerLimit,
      activeTabStyle: this.activeTabStyle,
      cssSelfText: this.cssSelfText,
      cssSelfUrl: this.cssSelfUrl,
    };
    const tabVaildateParams = {
      main: [
        "durationEnabled",
        "period",
        "editingDuration",
        "editingDurationEnabled",
        "duration",
        "kioskMode",
        "useLangs",
        "selectedLangs",
        "defaultLang",
        "pointSystem",
        "personalData",
        "showFoquzLink",
        "title",
        "description",
      ],
      fillingForm: [
        "timeToPass",
        "answersCount",
        "targetCount",
        "expirationTime",
        "timeToRestart",
        "timeToRestartFinish",
        "needAuthEnabled",
        "endPoll",
      ],
      sendForm: [
        "dontSendIfPassed",
        "stopSending",
        "stopSendingCondition",
        "stopSendingPeriod",
        "dontSendIfPromocodeUsed",
        "ignoreGlobalMailingFrequency",
        "pollMailingFrequency",
        "ignoreWidgetDisplay",
        "createWidgetNewAnswerLimit",
        "widget_display_ignore_limit",
        "widget_create_new_answer_limit",
        "stopSendingWidget",
        "stopSendingWidgetConditions",
        "stopSendingWidgetPeriod",
        "stopSendingWidgetTime",
        "dontSendIfPassedLink",
        "stopSendingLink",
        "stopSendingLinkCondition",
        "stopSendingLinkPeriod",
        "stopSendingLinkPeriodTime",
      ],
      processingAnswers: [
        "moderatorId",
        "executorId",
        "processingTime",
        "openProcessingByLink",
        "processingByLinkTime",
        "openProcessingForClient",
        "processingForClientTime",
        "notificationScript",
      ],
      other: ["dictionaryId", "activeTabStyle", "cssSelfText", "cssSelfUrl"],
      requaredParams: ["need_auth"],
    };
    const uniqueParams = Object.keys(allParams).filter(
      (param) =>
        !Object.values(tabParams).some((paramsArray) =>
          paramsArray.includes(param)
        )
    );

    /** При закрытии страницы сообщать о наличии изменений */
    this.hasChanges = ko.observable(false);

    function getActiveTabParams(activeTab) {
      const params = tabVaildateParams[activeTab];
      const result = {};
      console.log("paramsparams", params);
      console.log("allParamsallParams", allParams);
      params.forEach((param) => {
        if (allParams[param] !== undefined) {
          result[param] = allParams[param];
        }
      });

      // Добавить уникальные параметры ко всем вкладкам
      // uniqueParams.forEach((param) => {
      //   if (allParams[param] !== undefined) {
      //     result[param] = allParams[param];
      //   }
      // });
      return result;
    }
    const activeTabParams = getActiveTabParams(this.activeTab());

    this.formModel(activeTabParams, {
      live: true,
      deep: true,
    });

    Object.entries(this.formModel()).forEach(([key, value]) => {
      value.subscribe((v) => {
        console.log("value: " + v);
        this.hasChanges(true);
      });
    });

    this.activeTab.subscribe((v) => {
      const activeTabParams = getActiveTabParams(this.activeTab());
      console.log("activeTabParams", activeTabParams);
      this.formModel(activeTabParams, {
        live: true,
        deep: true,
      });
      console.log("formModel", this.formModel());
      console.log("allFormModel", allParams);
      Object.entries(this.formModel()).forEach(([key, value]) => {
        console.log(key, value);
        value.subscribe((v) => {
          console.log("value: " + v);
          this.hasChanges(true);
        });
      });
    });

    /** Установка сохраненных значений */
    this.setPollData(pageData.poll);

    window.onbeforeunload = () => {
      if (!this.blocked && this.hasChanges()) {
        return true;
      }
    };

    this.notificationScript.subscribe((value) => {
      this.hasChanges(true);
    });
    this.stopSendingWidget.subscribe((v) => {
      if (!v) {
        this.stopSendingWidgetTime('')
      }
    })

    // ИИ обработка открытых ответов
    this.openAiDialog = () => {
      this.openDialog({
        name: "ai-processing-request-dialog",
        params: {
          url: "poll/send-ai-processing-request",
        },
      });
    };
    this.onAiToggleClick = () => {
      if (this.aiControlDisabled()) {
        this.openAiDialog();
      } else {
        this.aiEnabled(!this.aiEnabled());
      }
    };
    this.companyAiEnabled.subscribe((v) => {
      if (!v) {
        this.aiEnabled(false);
      }
    });
    this.isRequestProcessingEnabled.subscribe((v) => {
      if (!v) {
        this.aiEnabled(false);
      }
    });
  }

  forceResizeTextarea() {
    const textarea = document.querySelector('#css-code-input textarea');
    if (textarea) {
      const event = new Event('input', { bubbles: true });
      textarea.dispatchEvent(event);
    }
  }

  setTab(tab) {
    if (this.hasChanges()) {
      this.deleteModalOpen({
        data: {
          submit: () => {
            this.changeUrl(tab);
            this.reset();
          },
        },
        close: () => {},
      });
    } else this.changeUrl(tab);
  }

  /** Устанавливает данные опроса в форму */
  setPollData(data) {
    console.log("data", data);
    this.pollData(data);

    (() => {
      let startDate = data.date_start;
      let endDate = data.date_end;

      if (startDate) {
        this.period({
          from: formatServerDateToClient(startDate),
          to: formatServerDateToClient(endDate),
        });
      } else {
        this.period({
          from: "",
          to: "",
        });
      }
    })();

    this.targetCount(data.goals_count);
    this.answersCount(data.limit_count);

    this.duration.update({
      from: data.datetime_start,
      to: data.datetime_end,
    });

    this.endPoll(data.end_of_question);

    this.timeToPass(data.time_to_pass || "");
    this.moderatorId(data.default_moderator_id ?? "");
    this.executorId(data.default_executor_id ?? "");

    this.aiEnabled(!!data.ai_enabled);

    if (data.dictionary_id) {
      this.dictionaryId(`${data.dictionary_id}`);
    } else this.dictionaryId(null);

    this.expirationTime.value = data.expiration_in_minutes || "";

    this.timeToRestart(data.time_to_restart ?? "");
    this.timeToRestartFinish(data.time_to_restart_screen_type_end ?? "");

    this.processingTime.value = data.processing_time_in_minutes || "";
    this.processingByLinkTime.value = data.processing_time_by_link_in_minutes;
    this.processingForClientTime.value =
      data.processing_time_for_client_in_minutes || "";
    this.openProcessingByLink(!!this.processingByLinkTime.value);
    this.openProcessingForClient(!!this.processingForClientTime.value);

    this.notificationScript(data.notification_script_id);

    this.title(data.title || "");
    this.widget_display_ignore_limit(data.widget_display_ignore_limit !== null ? data.widget_display_ignore_limit : '');
    this.widget_create_new_answer_limit(data.widget_create_new_answer_limit !== null ? data.widget_create_new_answer_limit : '');
    this.description(data.goal_text);

    this.kioskMode(!!data.kiosk_mode);
    this.pointSystem(!!data.point_system);
    this.dontSendIfPassed(!!data.dont_send_if_passed);
    this.dontSendIfPassedLink(!!data.dont_send_if_passed_link);
    this.dontSendIfPromocodeUsed(!!data.dont_send_if_promocode_used);
    this.personalData(!!data.personal_data);
    this.showFoquzLink(!!data.show_foquz_link);

    const langs = data.foquzPollLangs;
    if (langs && langs.length) {
      console.log("langs", langs);
      const defaultLang = langs.find((l) => l.default);
      this.defaultLang(
        (defaultLang && defaultLang.poll_lang_id) || DEFAULT_LANG
      );
      this.selectedLangs(langs.map((l) => l.poll_lang_id));
      console.log(this.selectedLangs());
      this.useLangs(true);
    } else {
      this.useLangs(false);
      this.defaultLang(null);
      this.selectedLangs([]);
    }

    this.utm.update({
      source: data.utm_source,
      medium: data.utm_medium,
      campaign: data.utm_campaign,
    });

    if (data.stopSendingCondition) {
      this.stopSendingCondition(data.stopSendingCondition);
      this.stopSendingPeriod(data.stopSendingPeriod);
      this.stopSending(true);
    } else {
      this.stopSendingCondition("double");
      this.stopSendingPeriod("");
      this.stopSending(false);
    }

    if (data.stopSendingLinkCondition) {
      this.stopSendingLinkCondition(data.stopSendingLinkCondition);
      this.stopSendingLinkPeriod(data.stopSendingLinkPeriod);
      this.stopSendingLinkPeriodTime(data.stopSendingLinkPeriodTime);
      this.stopSendingLink(true);
    } else {
      this.stopSendingLinkCondition("double");
      this.stopSendingLinkPeriod("");
      this.stopSendingLinkPeriodTime("");
      this.stopSendingLink(false);
    }
    if (data.widget_display_limit_type) {
      this.stopSendingWidgetCondition(data.widget_display_limit_type);
      this.stopSendingWidgetPeriod(data.widget_display_limit_days);
      this.stopSendingWidgetTime(data.widget_display_limit_time);
      this.stopSendingWidget(true);
    } else {
      this.stopSendingWidgetCondition("1");
      this.stopSendingWidgetPeriod("");
      this.stopSendingWidgetTime("");
      this.stopSendingWidget(false);
    }

    this.durationEnabled(
      data.datetime_start || data.datetime_end ? true : false
    );
    this.ignoreGlobalMailingFrequency(data.mailing_limit);

    this.ignoreWidgetDisplay(data.widget_display_ignore_limit !== null);
    this.createWidgetNewAnswerLimit(data.widget_create_new_answer_limit !== null);
    this.pollMailingFrequency(
      "mailing_frequency" in data && data.mailing_frequency !== null
        ? data.mailing_frequency
        : ""
    );

    if (!isNaN(parseInt(data.editing_duration)) && data.editing_duration >= 0) {
      this.editingDurationEnabled(true);
      if (data.editing_duration == 0) {
        this.editingDuration("");
      } else {
        this.editingDuration(data.editing_duration);
      }
    } else {
      this.editingDurationEnabled(false);
    }

    this.needAuthEnabled(data.need_auth);

    this.activeTabStyle(data.css_self_type);
    this.cssSelfUrl(data.css_self_url);
    this.cssSelfText(data.css_self_text);

    setTimeout(() => {
      this.hasChanges(false);
    });
  }

  /** Собирает данные формы для отправки */
  getParams() {
    let params = {
      goalCount: this.targetCount(),
      limit_count: this.answersCount(),
      title: this.title(),
      goal: this.description(),
      point_system: this.pointSystem() ? 1 : 0,
      dont_send_if_passed: this.dontSendIfPassed() ? 1 : 0,
      dont_send_if_passed_link: this.dontSendIfPassedLink() ? 1 : 0,
      dont_send_if_promocode_used: this.dontSendIfPromocodeUsed() ? 1 : 0,
      widget_display_ignore_limit: this.ignoreWidgetDisplay()
        ? this.widget_display_ignore_limit()
        : "",
      widget_create_new_answer_limit: this.createWidgetNewAnswerLimit()
      ? this.widget_create_new_answer_limit()
      : "",
      personal_data: this.personalData() ? 1 : 0,
      notificationScript: this.notificationScript(),
      time_to_pass: this.timeToPass(),
      time_to_restart: this.timeToRestart(),
      time_to_restart_screen_type_end: this.timeToRestartFinish(),
      processing_time_in_minutes: this.processingTime.value,
      processing_time_for_client_in_minutes: this.processingForClientTime.value,
      expiration_in_minutes: this.expirationTime.value,
      utm_source: this.utm.source(),
      utm_medium: this.utm.medium(),
      utm_campaign: this.utm.campaign(),
      default_moderator_id: this.moderatorId(),
      default_executor_id: this.executorId(),
      dictionary_id: this.dictionaryId() || "",
      need_auth: this.needAuthEnabled() ? 1 : 0,
      css_self_type: this.activeTabStyle(),
      ai_enabled: this.aiEnabled() ? 1 : 0,
    };

    if(this.activeTabStyle() == "0"){
      params.css_self_url = this.cssSelfUrl();
      params.css_self_text = null;
    } else if(this.activeTabStyle() == "1"){
      params.css_self_text = this.cssSelfText();
      params.css_self_url = null;
    }

    if (this.stopSending()) {
      params.stopSendingCondition = this.stopSendingCondition();
      if (this.stopSendingCondition() == "double") {
        params.stopSendingPeriod = "";
      } else if (this.stopSendingCondition() == "period") {
        params.stopSendingPeriod = this.stopSendingPeriod();
      }
    } else {
      params.stopSendingCondition = "";
      params.stopSendingPeriod = "";
    }

    if (this.stopSendingLink()) {
      params.stopSendingLinkCondition = this.stopSendingLinkCondition();
      if (this.stopSendingLinkCondition() == "double") {
        params.stopSendingLinkPeriod = "";
      } else if (this.stopSendingLinkCondition() == "period") {
        params.stopSendingLinkPeriod = this.stopSendingLinkPeriod();
        params.stopSendingLinkPeriodTime = this.stopSendingLinkPeriodTime();
      }
    } else {
      params.stopSendingLinkCondition = "";
      params.stopSendingLinkPeriod = "";
    }
    if (this.stopSendingWidget()) {
      if (this.stopSendingWidgetCondition() == 1) {
        params.widget_display_limit_type = this.stopSendingWidgetCondition();
        params.widget_display_limit_days = "";
        params.widget_display_limit_time = "";
      } else if (this.stopSendingWidgetCondition() == 2) {
        params.widget_display_limit_type = this.stopSendingWidgetCondition();
        params.widget_display_limit_days = this.stopSendingWidgetPeriod();
        params.widget_display_limit_time = this.stopSendingWidgetTime();
      }
    } else {
      params.widget_display_limit_type = 0;
      params.widget_display_limit_days = "";
      params.widget_display_limit_time = "";
    }

    if (this.editingDurationEnabled()) {
      let duration = this.editingDuration();
      if (isNaN(parseInt(duration)) || !(duration >= 0)) {
        duration = 0;
      }
      params.editing_duration = duration;
    } else {
      params.editing_duration = null;
    }
    if (this.isAuto()) {
      let { from, to } = this.period();
      console.log(from, to);
      params = {
        ...params,
        dateRange: [from, to].filter(Boolean).join(" - "),
        endOffPoll: this.endPoll(),
      };
    } else {
      let { from, to } = this.duration.getValue();
      params = {
        ...params,
        kioskMode: this.kioskMode() ? 1 : 0,
        datetime_start: this.durationEnabled() ? from : "",
        datetime_end: this.durationEnabled() ? to : "",
        processing_time_by_link_in_minutes: this.processingByLinkTime.value,
      };
    }
    if (this.companyTariffId === 3) {
      params.show_foquz_link = this.showFoquzLink() ? 1 : 0;
    }

    if (this.defaultLang() || this.selectedLangs().length) {
      params.defaultLang = this.defaultLang();
      params.lang = this.selectedLangs();
    }

    return params;
  }

  /** Отправка данных */
  async submit() {
    this.isSubmitted(true);

    var errors = ko.validation.group(this.formModel());

    // есть ли ошибки валидации у параметров, которые не проходят первыую валидацию
    const uniqIsValid =
      (this.duration.periodError() && this.durationEnabled()) ||
      !this.processingTime.time.isValid() ||
      !this.processingForClientTime.time.isValid();

    if (!this.formModel.isValid() || uniqIsValid) return;

    const params = this.getParams();
    console.log("params", params);
    const extractParams = (category, paramsList) => {
      let keys = [...tabParams[category], ...requaredParams];

      if (!keys) return {};

      let result = {};
      for (let key of keys) {
        if (params.hasOwnProperty(key)) {
          result[key] = params[key];
        }
      }
      return result;
    };

    let finalParams = extractParams(this.activeTab(), tabParams);

    await $.post(
      `/foquz/api/poll/mailing-settings?access-token=${APIConfig.apiKey}&id=${
        this.pollData().id
      }`,
      {
        ["mailing_frequency"]: this.ignoreGlobalMailingFrequency()
          ? this.pollMailingFrequency()
          : 0,
        ["mailing_limit"]: this.ignoreGlobalMailingFrequency() ? 1 : 0,
      }
    );

    const response = await $.ajax({
      url: ApiUrl("poll/save-settings", { id: this.pollData().id }),
      method: "POST",
      data: finalParams,
    });

    this.showSuccessMessage(true);
    PollSettingsUpdateEvent.emit({
      id: this.pollData().id,
      settings: response.poll,
    });
    PollActivateEvent.emit({ id: this.pollData().id });

    this.setPollData(response.poll);
    this.hasChanges(false);
    this.isSubmitted(false);
  }

  /** Сброс данных формы */
  reset() {
    this.setPollData(this.pollData());
    this.hasChanges(false);

    this.forceResizeTextarea();
  }

  /** Предупреждение при выключение переключателя Согласие на обработку данных */
  showPersonalDataWarning() {
    this.info({
      text: this.pageTranslator.t(
        "Если в опросе используются или собираются личные данные респондентов, то опция {openingTag}должна быть обязательно включена{closingTag}.",
        {
          openingTag: '<span class="f-color-danger">',
          closingTag: "</span>",
        }
      ),
    }).then(() => {
      this.personalData(false);
    });
  }

  onDictionarySelectClick() {
    if (this.companyCollectionsLoaded()) {
      return true;
    }
    const self = this;
    return getCompanyCollections(false, { for_select: true }).then((data) => {
      self.companyCollections.push(
        ...data
          .filter(
            (el) =>
              !el.system &&
              (!self.dictionaryId() || el.id != self.dictionaryId())
          )
          .map((el) => ({ ...el, text: el.name }))
      );
      self.companyCollectionsLoaded(true);
    });
  }

  onDictionarySelecting(event, ctx) {
    if (ctx.pollData().hasDictionaryLinks) {
      event.preventDefault();
      this.confirm({
        title: "Изменение связанного справочника",
        text: "При изменении справочника все связки в настройках опроса будут удалены",
        mode: "secondary",
        confirm: "Применить изменение",
      }).then(() => {
        if (event.type === "select2:unselecting") {
          this.dictionaryId("");
          return;
        }
        this.dictionaryId(event.params.args.data.id);
      });
      return true;
    }
    return true;
  }

  onRender() {}
}

const viewModel = new ViewModel(window.pageData);
ko.applyBindings(viewModel, document.querySelector("[data-page]"));
